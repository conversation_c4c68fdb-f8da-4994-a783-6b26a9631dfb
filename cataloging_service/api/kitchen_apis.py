from cataloging_service.infrastructure.decorators import raw_json
from cataloging_service.domain import service_provider
from flask import Blueprint, jsonify

from cataloging_service.api.schemas import KitchenSchema

from cataloging_service.api.request_objects import KitchenCreateRequest, KitchenUpdateRequest
from cataloging_service.api.validators import KitchenCreateRequestValidator, KitchenEditRequestValidator
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import raw_json

bp = Blueprint('kitchen_apis', __name__)

kitchen_service = service_provider.kitchen_service


@bp.route('/v1/properties/<string:property_id>/kitchens', methods=['POST'])
@raw_json(KitchenCreateRequestValidator)
def create_kitchen(property_id, parsed_request):
    """
    ---
    post:
        tags:
            - Kitchen
        description: Create Kitchen
        parameters:
            - in: path
              name: property_id
              description: The property_id to be assigned to the newly created Kitchen
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/KitchenCreateRequestValidator"
        responses:
            201:
                description: Created Kitchen object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/KitchenSchema"
    """
    kitchen_create_request = KitchenCreateRequest(property_id, parsed_request)
    created_kitchen = kitchen_service.create(kitchen_create_request)
    return jsonify(KitchenSchema().dump(created_kitchen).data), 201


@bp.route('/v1/properties/<string:property_id>/kitchens', methods=['GET'])
def get_kitchens(property_id):
    """
    ---
    get:
        tags:
            - Kitchen
        description: Get All Kitchens
        parameters:
            - name: property_id
              in: path
              description: Property Id to fetch all kitchens for
              required: True
              schema:
                    type: string
        responses:
            200:
                description: Get all kitchens.
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/KitchenSchema"
    """
    kitchens = kitchen_service.get_kitchens_in_property(property_id=property_id)
    return jsonify(KitchenSchema().dump(kitchens, many=True).data), 200


@bp.route('/v1/properties/<string:property_id>/kitchens/<string:kitchen_id>', methods=['GET'])
def get_kitchen(property_id, kitchen_id):
    """
    ---
    get:
        tags:
            - Kitchen
        parameters:
            - name: property_id
              in: path
              description: Property Id to fetch kitchen for
              required: True
              schema:
                  type: string
            - name: kitchen_id
              in: path
              description: Kitchen Id to fetch kitchen for
              required: True
              schema:
                  type: int
        description: Get Kitchen Details
        responses:
            200:
                description: Get Kitchen object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/KitchenSchema"
    """
    kitchen = kitchen_service.get_kitchen(kitchen_id=kitchen_id)
    return jsonify(KitchenSchema().dump(kitchen).data), 200


@bp.route('/v1/properties/<string:property_id>/kitchens/<int:kitchen_id>', methods=['PATCH'])
@raw_json(KitchenEditRequestValidator)
def update_kitchen(property_id, kitchen_id, parsed_request):
    """
    ---
    patch:
        tags:
            - Kitchen
        description: Edit Kitchen
        parameters:
            - name: property_id
              in: path
              description: Property Id  to edit kitchen for
              required: True
              schema:
                    type: string
            - name: kitchen_id
              in: path
              description: Kitchen Id to edit kitchen for
              required: True
              schema:
                    type: int
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/KitchenEditRequestValidator"
        responses:
            200:
                description: Edited kitchen object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/KitchenSchema"
    """
    kitchen_update_request = KitchenUpdateRequest(kitchen_id=kitchen_id, property_id=property_id,
                                                  dictionary=parsed_request)
    updated_kitchen = kitchen_service.update(kitchen_id,  kitchen_update_request)
    return jsonify(KitchenSchema().dump(updated_kitchen).data), 200


@bp.route('/v1/properties/<string:property_id>/kitchens/<int:kitchen_id>', methods=['DELETE'])
def delete_kitchen(property_id, kitchen_id):
    """
    ---
    delete:
        tags:
            - Kitchen
        parameters:
            - name: property_id
              in: path
              description: Kitchen Id to delete kitchen for
              required: True
              schema:
                  type: string
            - name: kitchen_id
              in: path
              description: Kitchen Id to delete kitchen for
              required: True
              schema:
                  type: int
        description: Kitchen Menu
        responses:
            204:
                description: The resource was deleted successfully.
    """
    kitchen_service.soft_delete(kitchen_id=kitchen_id, property_id=property_id)
    return jsonify({}), 204
