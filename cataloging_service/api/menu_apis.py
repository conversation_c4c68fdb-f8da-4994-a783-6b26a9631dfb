from cataloging_service.infrastructure.decorators import query_params, raw_json
from cataloging_service.domain import service_provider
from flask import Blueprint, jsonify, request

from cataloging_service.api.schemas import MenuComboSchema, MenuSchema, MenuDetailSchema, MenuItemListSchema,\
    MenuItemSchema, MenuListWithMenuItemsAndCategoriesSchema

from cataloging_service.api.request_objects import MenuComboEditRequest, MenuCreateRequest, MenuEditRequest,\
    MenuItemEditRequest
from cataloging_service.api.validators import MenuComboEditRequestValidator, MenuCreateRequestValidator,\
    MenuEditRequestValidator, MenuItemEditRequestValidator, MenuSearchRequestValidator
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import raw_json, query_params

bp = Blueprint('menu_apis', __name__)

menu_service = service_provider.menu_service


@bp.route('/v1/sellers/<string:seller_id>/menus', methods=['POST'])
@raw_json(MenuCreateRequestValidator)
def create_menu(seller_id, parsed_request):
    """
    ---
    post:
        tags:
            - Menu
        description: Create Menu
        parameters:
            - in: path
              name: seller_id
              description: The seller_id to be assigned to the newly created Menu
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/MenuCreateRequestValidator"
        responses:
            201:
                description: Created menu object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/MenuDetailSchema"
    """
    menu_create_request = MenuCreateRequest(seller_id, parsed_request)
    created_menu = menu_service.create_menu(menu_create_request)
    return jsonify(MenuDetailSchema().dump(created_menu).data), 201


@bp.route('/v1/sellers/<string:seller_id>/menus', methods=['GET'])
@query_params(MenuSearchRequestValidator)
def get_all_menus(seller_id, parsed_request):
    """
    ---
    get:
        tags:
            - Menu
        description: Get All Menus
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch all menus for
              required: True
              schema:
                    type: string
            - name: name
              in: query
              description: Name to filter by all menus for
              required: False
              schema:
                    type: string
        description: Get All Menus
        responses:
            200:
                description: Get all menus.
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/MenuSchema"
    """
    compressed = parsed_request.get("compressed", True)
    menu_ids = parsed_request.get("menu_ids")
    if menu_ids:
        menu_ids = menu_ids.split(',')

    if not compressed:
        menus = menu_service.get_menus_with_categories_and_item_counts(seller_id=seller_id, menu_ids=menu_ids)
        return jsonify(MenuListWithMenuItemsAndCategoriesSchema().dump(menus, many=True).data), 200

    menus = menu_service.get_menus(seller_id=seller_id, filters=parsed_request, menu_ids=menu_ids)
    return jsonify(MenuSchema().dump(menus, many=True).data), 200


@bp.route('/v1/sellers/<string:seller_id>/menus/<int:menu_id>', methods=['GET'])
def get_menu(seller_id, menu_id):
    """
    ---
    get:
        tags:
            - Menu
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch menu for
              required: True
              schema:
                  type: string
            - name: menu_id
              in: path
              description: Menu Id to fetch menu for
              required: True
              schema:
                  type: int
        description: Get Menu Details
        responses:
            200:
                description: Get menu object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/MenuDetailSchema"
    """
    menu = menu_service.get_menu(menu_id)
    return jsonify(MenuDetailSchema().dump(menu).data), 200


@bp.route('/v1/sellers/<string:seller_id>/menus/<int:menu_id>', methods=['PATCH'])
@raw_json(MenuEditRequestValidator)
def edit_menu(seller_id, menu_id, parsed_request):
    """
    ---
    patch:
        tags:
            - Menu
        description: Edit Menu
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to edit menu for
              required: True
              schema:
                    type: string
            - name: menu_id
              in: path
              description: Menu Id to edit menu for
              required: True
              schema:
                    type: int
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/MenuEditRequestValidator"
        responses:
            200:
                description: Edited menu object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/MenuDetailSchema"
    """
    menu_edit_request = MenuEditRequest(menu_id=menu_id, seller_id=seller_id, dictionary=parsed_request)
    edited_menu = menu_service.edit_menu(menu_id, seller_id, menu_edit_request)
    return jsonify(MenuDetailSchema().dump(edited_menu).data), 200


@bp.route('/v1/sellers/<string:seller_id>/menus/<int:menu_id>/menu-combos/<int:menu_combo_id>', methods=['PATCH'])
@raw_json(MenuComboEditRequestValidator)
def edit_menu_combo(seller_id, menu_id, menu_combo_id, parsed_request):
    """
    ---
    patch:
        tags:
            - Menu Combo
        description: Edit Menu Combo
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to edit menu combo for
              required: True
              schema:
                    type: string
            - name: menu_combo_id
              in: path
              description: Menu Combo Id to edit menu combo for
              required: True
              schema:
                    type: int
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/MenuComboEditRequestValidator"
        responses:
            200:
                description: Edited menu combo object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/MenuComboSchema"
    """
    menu_combo_edit_request = MenuComboEditRequest(menu_combo_id=menu_combo_id, dictionary=parsed_request)
    edited_menu_combo = menu_service.edit_menu_combo(
        menu_combo_id=menu_combo_id, seller_id=seller_id, menu_id=menu_id, menu_combo_edit_request=menu_combo_edit_request)
    return jsonify(MenuComboSchema().dump(edited_menu_combo).data), 200


@bp.route('/v1/sellers/<string:seller_id>/menu-items', methods=['GET'])
@query_params(MenuSearchRequestValidator)
def get_menu_items(seller_id, parsed_request):
    """
    ---
    get:
        tags:
            - Menu Item
        description: Get All Menu Items
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch all menu-items for
              required: True
              schema:
                    type: string
            - name: name
              in: query
              description: Name to filter by all menu-items for
              required: False
              schema:
                    type: string
            - name: food_type
              in: query
              description: Filter all items via the food_type attribute
              required: False
              schema:
                    type: string
        description: Get All Menu-Items
        responses:
            200:
                description: Get all menu-items.
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/MenuItemListSchema"
    """
    all_menu_items = menu_service.get_menu_items(seller_id=seller_id, parsed_request=parsed_request)
    return jsonify(MenuItemListSchema().dump(all_menu_items, many=True).data), 200


@bp.route('/v1/sellers/<string:seller_id>/menu-items/<int:menu_item_id>', methods=['GET'])
def get_menu_item(seller_id, menu_item_id):
    """
    ---
    get:
        tags:
            - Menu Item
        description: Get Menu Item
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to get menu item for
              required: True
              schema:
                    type: string
            - name: menu_item_id
              in: path
              description: Menu Item Id to get menu item for
              required: True
              schema:
                    type: int
        responses:
            200:
                description: menu item object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/MenuItemSchema"
    """
    menu_item = menu_service.get_menu_item(seller_id=seller_id, menu_item_id=menu_item_id)
    return jsonify(MenuItemSchema().dump(menu_item).data), 200


@bp.route('/v1/sellers/<string:seller_id>/menus/<int:menu_id>/menu-items/<int:menu_item_id>', methods=['PATCH'])
@raw_json(MenuItemEditRequestValidator)
def edit_menu_item(seller_id, menu_id, menu_item_id, parsed_request):
    """
    ---
    patch:
        tags:
            - Menu Item
        description: Edit Menu Item
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to edit menu item for
              required: True
              schema:
                    type: string
            - name: menu_item_id
              in: path
              description: Menu Item Id to edit menu item for
              required: True
              schema:
                    type: int
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/MenuItemEditRequestValidator"
        responses:
            200:
                description: Edited menu item object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/MenuItemSchema"
    """
    menu_item_edit_request = MenuItemEditRequest(menu_item_id=menu_item_id, dictionary=parsed_request)
    edited_menu_item = menu_service.edit_menu_item(
        menu_item_id=menu_item_id,  menu_item_edit_request=menu_item_edit_request, seller_id=seller_id)
    return jsonify(MenuItemSchema().dump(edited_menu_item).data), 200


@bp.route('/v1/sellers/<string:seller_id>/menus/<int:menu_id>', methods=['DELETE'])
def delete_menu(seller_id, menu_id):
    """
    ---
    delete:
        tags:
            - Menu
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to delete menu for
              required: True
              schema:
                  type: string
            - name: menu_id
              in: path
              description: Menu Id to delete menu for
              required: True
              schema:
                  type: int
        description: Delete Menu
        responses:
            204:
                description: The resource was deleted successfully.
    """
    menu_service.soft_delete_menu(menu_id=menu_id, seller_id=seller_id)
    return jsonify({}), 204
