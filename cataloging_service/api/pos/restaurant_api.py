import logging

from flask import jsonify, Blueprint

from cataloging_service.api.response_schema import RestaurantTablesResponseSchema
from cataloging_service.domain import service_provider

logger = logging.getLogger(__name__)

seller_service = service_provider.seller_service

bp = Blueprint('pos_restaurant', __name__, url_prefix='/cataloging-service/api/v1')


@bp.route('/sellers/<string:seller_id>/tables', methods=['GET'])
def get_restaurant_tables(seller_id):
    """
    Get restaurant tables, for the given seller_id
    ---
    operationId: get_restaurant_tables
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - POS
        parameters:
            - name: seller_id
              in: path
              required: True
              schema:
                type: string
        description: Get restaurant tables, for the given seller_id
        responses:
            '200':
                description: List of tables
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                skus:
                                    type: array
                                    items:
                                        $ref: '#/components/schemas/RestaurantTablesResponseSchema'
    """
    restaurant_tables = seller_service.get_restaurant_tables(seller_id=seller_id)
    response = RestaurantTablesResponseSchema(many=True).dump(restaurant_tables).data
    return jsonify({"tables": response}), 200
