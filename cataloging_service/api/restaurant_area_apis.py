from flask import Blueprint, jsonify, request

from cataloging_service.domain import service_provider
from cataloging_service.api.schemas import RestaurantAreaSchema, RestaurantAreaDetailSchema, RestaurantTableSchema
from cataloging_service.api.request_objects import RestaurantAreaCreateRequest, RestaurantTableBulkCreateRequest,\
    RestaurantAreaUpdateRequest
from cataloging_service.api.validators import RestaurantAreaCreateRequestValidator,\
    RestaurantTableBulkCreateRequestValidator, RestaurantAreaUpdateRequestValidator
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import raw_json

bp = Blueprint('table_apis', __name__)
restaurant_area_service = service_provider.restaurant_area_service


@bp.route('/v1/sellers/<string:seller_id>/areas', methods=['POST'])
@raw_json(RestaurantAreaCreateRequestValidator)
def create_area(seller_id, parsed_request):
    """
    ---
    post:
        tags:
            - Restaurant Area
        description: Create Area
        parameters:
            - in: path
              name: seller_id
              description: The seller_id to be assigned to the newly created Area
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/RestaurantAreaCreateRequestValidator"
        responses:
            201:
                description: Created Area object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RestaurantAreaDetailSchema"
    """
    area_create_request = RestaurantAreaCreateRequest(seller_id, parsed_request)
    created_area = restaurant_area_service.create_area(area_create_request)
    return jsonify(RestaurantAreaDetailSchema().dump(created_area).data), 201


@bp.route('/v1/sellers/<string:seller_id>/areas/<string:area_id>/tables', methods=['POST'])
@raw_json(RestaurantTableBulkCreateRequestValidator)
def create_tables(seller_id, area_id, parsed_request):
    """
    ---
    post:
        tags:
            - Restaurant Area
        description: Create Tables
        parameters:
            - in: path
              name: seller_id
              description: The seller_id to be assigned to the newly created Area
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/RestaurantTableBulkCreateRequestValidator"
        responses:
            201:
                description: Area object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RestaurantAreaDetailSchema"
    """
    table_create_request = RestaurantTableBulkCreateRequest(seller_id, parsed_request)
    area = restaurant_area_service.add_tables_to_area(seller_id, area_id, table_create_request)
    return jsonify(RestaurantAreaDetailSchema().dump(area).data), 201


@bp.route('/v1/sellers/<string:seller_id>/areas/<string:area_id>', methods=['PATCH'])
@raw_json(RestaurantAreaUpdateRequestValidator)
def update_area(seller_id, area_id, parsed_request):
    """
    ---
    post:
        tags:
            - Restaurant Area
        description: Update Area
        parameters:
            - in: path
              name: seller_id
              description: The seller_id of the Area
              required: True
              schema:
                    type: string
        requestBody:
            required: true
            content:
                application/json:
                    schema:
                        $ref: "#/components/schemas/RestaurantAreaUpdateRequestValidator"
        responses:
            200:
                description: Area object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RestaurantAreaDetailSchema"
    """
    area_update_request = RestaurantAreaUpdateRequest(area_id, parsed_request)
    area = restaurant_area_service.update_restaurant_area(seller_id, area_update_request)
    return jsonify(RestaurantAreaDetailSchema().dump(area).data), 200


@bp.route('/v1/sellers/<string:seller_id>/areas', methods=['GET'])
def get_areas(seller_id):
    """
    ---
    get:
        tags:
            - Restaurant Area
        description: Get Restaurant Areas
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch restaurant areas for
              required: True
              schema:
                    type: string
        description: Get  Restaurant Areas
        responses:
            200:
                description: Get Restaurant Areas
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: "#/components/schemas/RestaurantAreaSchema"
    """
    restaurant_areas = restaurant_area_service.get_restaurant_areas(seller_id=seller_id)
    return jsonify(RestaurantAreaSchema().dump(restaurant_areas, many=True).data), 200


@bp.route('/v1/sellers/<seller_id>/areas/<area_id>', methods=['GET'])
def get_area(seller_id, area_id):
    """
    ---
    get:
        tags:
            - Restaurant Area
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to fetch restaurant area for
              required: True
              schema:
                  type: string
            - name: area_id
              in: path
              description: Area Id to fetch Restaurant Area for
              required: True
              schema:
                  type: int
        description: Get Restaurant Area Details
        responses:
            200:
                description: Get Restaurant Area object.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RestaurantAreaDetailSchema"
    """
    restaurant_area = restaurant_area_service.get_restaurant_area(seller_id=seller_id, area_id=area_id)
    return jsonify(RestaurantAreaDetailSchema().dump(restaurant_area).data), 200


@bp.route('/v1/sellers/<seller_id>/tables', methods=['GET'])
def get_tables(seller_id):
    """
    ---
    get:
        tags:
            - Restaurant Tables
        parameters:
            - name: seller_id
              in: path
              description: Fetch Restaurant Tables by seller_id
              required: True
              schema:
                  type: string
        description: Get all tables by seller
        responses:
            200:
                description: Get Seller Restaurant Tables Array.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RestaurantTableSchema"
    """
    tables = restaurant_area_service.get_tables(seller_id=seller_id)
    return jsonify(RestaurantTableSchema().dump(tables, many=True).data), 200


@bp.route('/v1/sellers/<seller_id>/tables/<table_id>', methods=['GET'])
def get_table(seller_id, table_id):
    """
    ---
    get:
        tags:
            - Restaurant Tables
        parameters:
            - name: seller_id
              in: path
              description: Fetch Restaurant Table by seller_id and table_id
              required: True
              schema:
                  type: string
        description: Get specific table by seller and table
        responses:
            200:
                description: Get Seller Restaurant Table.
                content:
                    application/json:
                        schema:
                            $ref: "#/components/schemas/RestaurantTableSchema"
    """
    table = restaurant_area_service.get_table(seller_id=seller_id, table_id=table_id)
    return jsonify(RestaurantTableSchema().dump(table).data), 200


@bp.route('/v1/sellers/<string:seller_id>/areas/<area_id>', methods=['DELETE'])
def delete_area(seller_id, area_id):
    """
    ---
    delete:
        tags:
            - Restaurant Area
        parameters:
            - name: seller_id
              in: path
              description: Seller Id to delete Restaurant Area for
              required: True
              schema:
                  type: string
            - name: area_id
              in: path
              description: Restaurant Area Id to delete area for
              required: True
              schema:
                  type: int
        description: Delete Restaurant Area
        responses:
            204:
                description: The resource was deleted successfully.
    """
    restaurant_area_service.soft_delete_area(area_id=area_id, seller_id=seller_id)
    return jsonify({}), 204