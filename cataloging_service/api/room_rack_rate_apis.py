import logging

from flask import Blueprint, jsonify, request
from newrelic.agent import add_custom_parameter

from cataloging_service.api.schemas import UpdateRoomRackRateSchema, RoomRackRateSchema
from cataloging_service.domain import service_provider
from cataloging_service.infrastructure.decorators import raw_json, log_request

bp = Blueprint('room_rack_rate_apis', __name__)

logger = logging.getLogger(__name__)

room_rack_rate_service = service_provider.room_rack_rate_service


@bp.route('/v1/properties/<string:property_id>/room-rack-rates/', methods=['GET'])
def get_room_rack_rates(property_id):
    """Get all room rack rate for given property
    ---
    operationId: get_room_rack_rates
    schemes: ['http', 'https']
    deprecated: false
    get:
        description: Get all room rack rate for given property
        tags:
            - Room
        parameters:
            - name: property_id
              in: path
              required: True
              schema:
                type: string
        responses:
            '200':
                description: RoomRackRate object
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: '#/components/schemas/RoomRackRateSchema'
    """
    room_rack_rates = room_rack_rate_service.get_room_rack_rates(property_id)
    return jsonify(
        dict(data=RoomRackRateSchema().dump(room_rack_rates, many=True).data)), 200


@bp.route('/v1/properties/<string:property_id>/room-rack-rates/<string:room_rack_rate_id>', methods=['PATCH'])
@raw_json(UpdateRoomRackRateSchema)
def update_room_rack_rate(property_id, room_rack_rate_id, parsed_request):
    """Update the given room rack rate
    ---
    operationId: update_room_rack_rate
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Update the given room rack rate
        tags:
            - Room
        parameters:
            - name: property_id
              in: path
              required: True
              schema:
                type: string
            - name: room_rack_rate_id
              in: path
              required: True
              schema:
                type: string
            - in: body
              name: body
              description: The action object which needs to be updated
              required: True
              schema:
                $ref: "#/components/schemas/UpdateRoomRackRateSchema"
        responses:
            200:
                description: The schema of the updated room rack rate.
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                message:
                                    type: string
    """
    room_rack_rate = room_rack_rate_service.update_room_rack_rate(room_rack_rate_id, parsed_request)
    return jsonify(
        dict(code="UPDATED", msg="Room Rack Rate Updated", data=RoomRackRateSchema().dump(room_rack_rate).data)), 200
