import logging

from flask import Blueprint, jsonify, request
from newrelic.agent import add_custom_parameter

from cataloging_service.api.schemas import SkuCategorySchema, SkuSchema, CustomPropertySkuSchema, NewSkuSchema, \
    UpdateSkuUnderPropertySchema, SkuSchemaV2
from cataloging_service.constants import error_codes
from cataloging_service.domain import service_provider
from cataloging_service.exceptions import CatalogingServiceException
from cataloging_service.extensions import cache
from cataloging_service.infrastructure.decorators import log_request
from cataloging_service.models import PropertySku
from cataloging_service.infrastructure.decorators import raw_json, log_request
from treebo_commons.request_tracing.log_filters import get_current_request_id

from cataloging_service.thread_locals import app_context

bp = Blueprint('category_apis', __name__)

logger = logging.getLogger(__name__)

property_service = service_provider.property_service
sku_service = service_provider.sku_service
seller_service = service_provider.seller_service


@bp.route('/sku_categories/', methods=['GET'])
def get_all_sku_categories_by_ids():
    # TODO: 0 calls
    """
    Get All SKU Categories by IDs
    ---
    operationId: get_all_sku_categories_by_ids
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU Category
        description: Get list of all SKU Categories
    """
    sku_category_ids = request.args.get('ids')
    if sku_category_ids:
        sku_category_ids = sku_category_ids.split(',')
        if not all(str.isdigit(sku_category_id) for sku_category_id in sku_category_ids):
            raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context='invalid sku category ids')

    sku_categories = sku_service.sget_all_sku_categories(sku_category_ids)
    payload = SkuCategorySchema().dump(sku_categories, many=True).data if sku_categories else []
    return jsonify(payload), 200


@bp.route('/sku_categories/codes/', methods=['GET'])
def get_all_sku_categories_by_codes():
    # TODO: 0 calls
    """
    Get All SKU Categories by codes
    ---
    operationId: get_all_sku_categories_by_codes
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU Category
        description: Get list of all SKU Categories
    """
    sku_category_codes = request.args.get('codes')
    if sku_category_codes:
        sku_category_codes = sku_category_codes.split(',')

    sku_categories = sku_service.sget_all_sku_categories_by_codes(sku_category_codes)
    payload = SkuCategorySchema().dump(sku_categories, many=True).data if sku_categories else []
    return jsonify(payload), 200


@bp.route('/v1/sku/', methods=['GET'])
def get_all_sku_by_codes():
    # TODO: 0 calls
    """
    Get All SKU by codes
    ---
    operationId: get_all_sku_by_codes
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU
        description: Get list of SKU
    """
    sku_codes = request.args.get('codes')
    page = request.args.get('page', 0, type=int)
    items_per_page = request.args.get('item_count', 20, type=int)
    if sku_codes:
        sku_codes = sku_codes.split(',')

    sku_s = sku_service.sget_all_sku_by_given_codes_and_pagination(page, items_per_page, sku_codes)
    payload = SkuSchema().dump(sku_s, many=True).data if sku_s else []
    return jsonify(payload), 200


@bp.route('/v1/sku/room_config/', methods=['GET'])
@log_request
def get_all_sku_by_room_configs():
    # Called from ITS sku_enabled_minimum_availability_across_days_for_occupancy_config
    # TODO: 11,012,001.15 calls
    """
    Get All SKU by room_configs
    ---
    operationId: get_all_sku_by_room_configs
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU
        description: Get list of SKU
    """
    add_custom_parameter('request_id', get_current_request_id())
    add_custom_parameter('params', request.args)

    hotel_ids = request.args.get('hotels')
    if hotel_ids:
        hotel_ids = hotel_ids.split(',')
    else:
        raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context="hotel_ids missing")
    configs = request.args.get('configs')
    if configs:
        configs = configs.split(',')
    else:
        raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context="configs missing")
    room_types = request.args.get('room_types')
    if room_types:
        room_types = room_types.split(',')

    cache_mode = request.args.get('cache_mode')

    @cache.multi_key_memoize(attribute_for_cache_key='hotel_ids', cache_key_id_getter=lambda item: item['property_id'],
                             timeout=3600, mode=cache_mode, unless=lambda: app_context.include_test)
    def payload(hotel_ids):
        property_wise_skus = sku_service.sget_all_skus_for_given_hotels(hotel_ids)
        response = [dict(property_id=property_id, property_sku_room_mapping=property_skus)
                    for property_id, property_skus in property_wise_skus.items()]
        return CustomPropertySkuSchema().dump(response, many=True).data if response else []

    result = payload(hotel_ids=hotel_ids)
    result = sku_service.sget_relevant_skus_for_given_room_config_and_hotels(result, room_types, configs)
    return jsonify(result), 200


@bp.route('/v1/get_room_config/', methods=['GET'])
# @cache.cached(timeout=3600, key_prefix=lambda: request.url)
def get_all_room_configs_by_sku():
    # TODO: Optimise this
    # TODO: 8,332.18 calls
    """
    Get All room_configs by sku_codes
    ---
    operationId: get_all_room_configs_by_sku
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - RoomConfig
        parameters:
            - name: skus
              in: query
              description: List of comma-separated sku_codes
              required: True
              schema:
                type: string
        description: Get list of room_configs
        responses:
            200:
                description: List of Room Configs
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                type: object
                                properties:
                                    sku_code:
                                        type: string
                                    room_type:
                                        type: string
                                    type:
                                        type: string
                                    room_config:
                                        type: string
                                    adults:
                                        type: integer
                                    children:
                                        type: integer
    """
    sku_codes = request.args.get('skus')
    if sku_codes:
        sku_codes = sku_codes.split(',')
    else:
        raise CatalogingServiceException(error_codes.INVALID_REQUEST_DATA, context="sku codes missing")
    result = sku_service.get_room_config_from_sku_redis(sku_codes)
    return jsonify(result), 200


@bp.route('/sku_categories/<int:sku_category_id>', methods=['GET'])
def get_sku_category(sku_category_id):
    # TODO: 0 calls
    """
    Get SKU Category by ID
    ---
    tags:
      - SKU Category
    summary: Get SKU Category by ID
    description: Get SKU Category for given sku_category_id
    parameters:
      - name: sku_category_id
        in: path
        required: true
        schema:
          type: integer
          format: int32
        description: The ID of the SKU category to retrieve
    responses:
      200:
        description: SKU Category object
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SkuCategorySchema'
      404:
        description: SKU Category not found
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIErrorSchema'
    """
    sku_category = sku_service.sget_sku_category(sku_category_id)
    sku_category = SkuCategorySchema().dump(sku_category).data
    return jsonify(sku_category or {}), 200


@bp.route('/sku_categories/<string:sku_code>', methods=['GET'])
def get_sku_category_by_code(sku_code):
    # TODO: 0 calls
    """
    Get SKU Category by sku_code
    ---
    operationId: get_sku_category_by_code
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU Category
        parameters:
            - name: sku_code
              in: path
              required: True
              schema:
                type: string
        description: Get SKU Category for given sku_code
        responses:
            '200':
                description: SkuCategory object
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SkuCategorySchema'
    """
    sku_category = sku_service.sget_sku_category_by_code(sku_code)
    sku_category = SkuCategorySchema().dump(sku_category).data
    return jsonify(sku_category or {}), 200


@bp.route('/properties/<string:property_id>/sku_categories/', methods=['GET'])
def get_property_sku_categories(property_id):
    # TODO: 0 calls
    """
    Get SKU Categories for given property id
    ---
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU Category
        parameters:
            - name: property_id
              in: path
              required: True
              schema:
                type: string
        description: Get SKU Categories for given property id
        responses:
            '200':
                description: SkuCategory object
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: '#/components/schemas/SkuCategorySchema'
    """
    sku_categories = property_service.get_sku_categories(property_id)
    payload = SkuCategorySchema().dump(sku_categories, many=True).data if sku_categories else []
    return jsonify(payload), 200


@bp.route('/properties/<string:property_id>/sku/', methods=['GET'])
def get_property_sku(property_id):
    # TODO: 1,802.76 calls
    """
    Get SKU for given property id
    ---
    operationId: get_property_sku
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU
        description: Get SKU for given property id
        parameters:
            - name: property_id
              in: path
              required: True
              schema:
                type: string
            - name: codes
              in: query
              required: False
              schema:
                type: string
        responses:
            '200':
                description: SkuCategory object
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: '#/components/schemas/SkuSchema'
    """
    sku_codes = request.args.get('codes', [])

    filter = {
        'for_inclusions': request.args.get('for_inclusions') == 'true',
    }

    if sku_codes:
        sku_codes = sku_codes.split(',')

    # TODO: fix caching as it is not working properly with more than one arguments.
    # It ignores changes in the arguments (except first).
    # data = get_cached_property_sku(property_id, sku_codes, filter)

    sku_list = property_service.get_property_skus(property_id, sku_codes, filter)
    data = SkuSchema().dump(sku_list, many=True).data if sku_list else []

    return jsonify(data), 200


@bp.route('/v2/properties/<string:property_id>/skus', methods=['GET'])
def get_property_skus(property_id):
    # NOTE: New API
    """
    Get SKUs for given property id
    ---
    operationId: get_property_skus
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU
        description: Get SKUs for given property id
        parameters:
            - name: property_id
              in: path
              required: True
              schema:
                type: string
            - name: codes
              in: query
              required: False
              schema:
                type: string
            - name: sku_category_codes
              in: query
              required: False
              schema:
                type: string
            - name: for_inclusions
              in: query
              required: False
              schema:
                type: bool
        responses:
            '200':
                description: List of SKUs matching the filter criteria
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                $ref: '#/components/schemas/SkuSchemaV2'
    """
    sku_codes = request.args.get('codes', [])
    sku_category_codes = request.args.get('sku_category_codes', [])
    for_inclusions = request.args.get('for_inclusions') == 'true'

    if sku_category_codes:
        sku_category_codes = sku_category_codes.split(',')

    if sku_codes:
        sku_codes = sku_codes.split(',')

    sku_list = property_service.get_property_skus_v2(property_id,
                                                     sku_codes=sku_codes, sku_category_codes=sku_category_codes,
                                                     for_inclusions=for_inclusions)
    data = SkuSchemaV2().dump(sku_list, many=True).data if sku_list else []

    return jsonify(data), 200


@bp.route('/properties/sku/', methods=['GET'])
def get_properties_skus():
    # TODO: 0 calls
    """
    Get all skus for given property ids
    ---
    operationId: get_properties_skus
    schemes: ['http', 'https']
    deprecated: false
    get:
        tags:
            - SKU
        description: Get all skus for given property ids
        parameters:
            - name: property_id
              in: query
              required: True
              schema:
                  type: array
                  items:
                    type: string
        responses:
            '200':
                description: SkuCategory object
                content:
                    application/json:
                        schema:
                            type: array
                            items:
                                type: object
                                properties:
                                    property_id:
                                        type: string
                                    skus:
                                        type: array
                                        items:
                                            $ref: '#/components/schemas/SkuSchema'
    """
    properties = []
    property_ids = request.args.getlist('property_id')
    for property_id in property_ids:
        properties.append({"property_id": property_id, "skus": get_cached_property_sku(property_id)})
    return jsonify(properties), 200


@cache.clear_cache_on_model_change(
    model_classes_with_cache_key_getter={PropertySku: lambda x: x.property_id},
    default_memoize=True)
@cache.memoize(timeout=3600, unless=lambda: app_context.include_test)
def get_cached_property_sku(property_id, sku_codes, filter=None):
    sku_list = property_service.get_property_skus(property_id, sku_codes, filter)
    return SkuSchema().dump(sku_list, many=True).data if sku_list else []


@bp.route('/v1/properties/<string:property_id>/skus', methods=['POST'])
@raw_json(NewSkuSchema)
def create_sku_under_a_property(property_id, parsed_request):
    """Create a sku under a property
    ---
    operationId: create_sku_under_a_property
    schemes: ['http', 'https']
    deprecated: false
    post:
        description: Create a sku under a property
        tags:
            - SKU
        parameters:
            - in: body
              name: body
              description: The action object which needs to be created
              required: True
              schema:
                $ref: "#/components/schemas/NewSkuSchema"
        responses:
            200:
                description: The schema of the sku created.
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                message:
                                    type: string
    """
    user_type = request.headers.get("X-User-Type") or None
    property_sku_dict = {
        'property_id': property_id,
        'rack_rate': parsed_request.get('rack_rate'),
        'description': parsed_request.get('description'),
        'extra_information': parsed_request.get('extra_information'),
        'sell_separate': parsed_request.get('sell_separate'),
    }
    sku_dict = {
        'name': parsed_request.get('name'),
        'category_code': parsed_request.get('category_code'),
        'offering': parsed_request.get('offering'),
        'frequency': parsed_request.get('frequency'),
        'hsn_sac': parsed_request.get('hsn_sac'),
        'is_property_inclusion': parsed_request.get('is_property_inclusion'),
        'is_expense_item_sku': parsed_request.get('is_expense_item_sku', True)
    }

    sku, property_sku = sku_service.create_sku_under_a_property(property_sku_dict, sku_dict, user_type)

    return jsonify(dict(code="CREATED", msg="SKU Created", data=SkuSchema().dump(sku).data)), 200


@bp.route('/v1/properties/<string:property_id>/skus/<string:sku_code>', methods=['PATCH'])
@raw_json(UpdateSkuUnderPropertySchema)
def update_sku_under_a_property(property_id, sku_code, parsed_request):
    """Update sku under a property
    ---
    operationId: update_sku_under_a_property
    schemes: ['http', 'https']
    deprecated: false
    patch:
        description: Update sku under a property
        tags:
            - SKU
        parameters:
            - name: property_id
              in: path
              required: True
              schema:
                type: string
            - name: sku_code
              in: path
              required: True
              schema:
                type: string
            - in: body
              name: body
              description: The action object which needs to be updated
              required: True
              schema:
                $ref: "#/components/schemas/UpdateSkuUnderPropertySchema"
        responses:
            200:
                description: The schema of the sku updated.
                schema:
                    type: object
                    properties:
                        data:
                            type: object
                            properties:
                                message:
                                    type: string
    """
    user_type = request.headers.get("X-User-Type") or None
    property_sku_dict = {
        'property_id': property_id,
        'rack_rate': parsed_request.get('rack_rate'),
        'description': parsed_request.get('description'),
        'extra_information': parsed_request.get('extra_information'),
        'sell_separate': parsed_request.get('sell_separate'),
    }
    sku_dict = {}
    if parsed_request.get('offering'):
        sku_dict['offering'] = parsed_request['offering']
    if parsed_request.get('frequency'):
        sku_dict['frequency'] = parsed_request['frequency']

    sku, property_sku = sku_service.update_sku_under_a_property(sku_code, property_sku_dict, sku_dict, user_type)

    return jsonify(dict(code="UPDATED", msg="SKU Updated")), 200


@bp.route('/v1/sellers/<string:seller_id>/sku-categories', methods=['GET'])
def get_sku_categories_for_seller(seller_id):
    """
    Get SKU Categories for Seller
    ---
    tags:
      - SKU Category
    summary: Get SKU Categories for Seller
    description: Get All Sku Categories in seller
    parameters:
      - name: seller_id
        in: path
        required: true
        schema:
          type: string
        description: The ID of the seller to get SKU categories for
    responses:
      200:
        description: List of SKU categories for the seller
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/SkuCategorySchema'
      404:
        description: Seller not found
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/APIErrorSchema'
    """
    sku_categories = sku_service.get_sku_categories_for_seller(seller_id)
    return jsonify(SkuCategorySchema().dump(sku_categories, many=True).data), 200
