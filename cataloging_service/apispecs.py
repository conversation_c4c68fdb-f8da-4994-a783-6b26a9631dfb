from flasgger import Swagger
from apispec import APISpec
from apispec.exceptions import DuplicateComponentNameError
from apispec.ext.marshmallow import MarshmallowPlugin

from cataloging_service.api.amenity_apis import (
    get_property_amenities, get_property_amenity_summary, get_property_restaurants, get_property_bars,
    get_property_banquet_halls, get_room_amenities
)
from cataloging_service.api.channel_apis import (
    get_all_channels, get_channel, get_sub_channels_for_channel, get_all_applications, get_applications_for_channel,
    get_all_pricing_policies
)
from cataloging_service.api.combo_apis import create_combo, get_all_combos, get_combo, edit_combo
from cataloging_service.api.currency_conversion_rates import get_currency_conversion_rates, \
    create_currency_conversion_rate
from cataloging_service.api.item_apis import create_item, get_item, get_items, edit_item, search_variant_groups, \
    create_variant_group, \
    edit_variant_group, get_item_customisations, get_item_variants
from cataloging_service.api.location_apis import (
    get_countries, get_states, get_cities, get_clusters, get_localities, get_micro_markets, get_regions
)
from cataloging_service.api.menu_apis import create_menu, get_all_menus, get_menu, edit_menu, edit_menu_combo, \
    edit_menu_item
from cataloging_service.api.pos.restaurant_api import get_restaurant_tables
from cataloging_service.api.pos.seller_sku_api import get_seller_skus, get_menu_categories
from cataloging_service.api.pos.sellers_api import get_sellers
from cataloging_service.api.property_apis import (
    get_all_properties, create_property, get_property, get_properties_v2, modify_property, get_property_images,
    activate_sku_and_bundle, create_property_sku, get_property_by_pms_and_property_id,
    create_property_v1, get_property_v1, modify_property_v1, get_property_images_v1,
    get_property_by_pms_and_property_id_v1, get_all_test_properties)
from cataloging_service.api.provider_apis import get_provider, get_all_providers, get_provider_properties
from cataloging_service.api.response_schema import SellerResponseSchema, MenuCategoryResponseSchema, \
    SellerSkuResponseSchema, RestaurantTablesResponseSchema, TenantConfigResponseSchema, \
    UserDefinedEnumResponseSchema, \
    UserDefinedEnumValueResponseSchema, CurrencyConversionRateResponseSchema
from cataloging_service.api.restaurant_area_apis import create_area, get_areas, get_area, update_area, get_table, \
    get_tables, create_tables
from cataloging_service.api.room_apis import (
    add_room_type_configuration, update_room_type_configuration,
    get_room_type_configurations, add_property_room, update_property_room, get_property_rooms, get_room_types
)
from cataloging_service.api.room_rack_rate_apis import (get_room_rack_rates, update_room_rack_rate)
from cataloging_service.api.ruptub_legal_entity_apis import get_all_legal_entities, get_legal_entity_by_state_id
from cataloging_service.api.schemas import SkuCategorySchema, SkuSchema, MenuSchema, MenuDetailSchema, \
    ItemSchema, ComboSchema, ComboDetailSchema, ItemListSchema, VariantGroupSchema, MenuItemSchema, \
    ItemCustomisationSchema, ItemVariantShortSchema, NewSkuSchema, UpdateRoomRackRateSchema, \
    RoomRackRateSchema, UpdateSkuUnderPropertySchema, RestaurantAreaSchema, RestaurantTableSchema, \
    RestaurantAreaDetailSchema, KitchenSchema, SkuSchemaV2, MenuItemListSchema, MenuComboSchema
from cataloging_service.api.seller_type_apis import get_seller_type
from cataloging_service.api.sku_apis import (
    get_all_sku_categories_by_ids, get_all_sku_categories_by_codes,
    get_all_sku_by_codes, get_all_sku_by_room_configs, get_all_room_configs_by_sku, get_sku_category,
    get_sku_category_by_code, get_property_sku_categories, get_property_sku, get_properties_skus,
    create_sku_under_a_property, update_sku_under_a_property, get_sku_categories_for_seller, get_property_skus
)
from cataloging_service.api.menu_apis import create_menu, get_all_menus, get_menu, edit_menu, edit_menu_combo, \
    edit_menu_item
from cataloging_service.api.item_apis import create_item, get_item, get_items, edit_item,  search_variant_groups, \
    create_variant_group, edit_variant_group, get_item_customisations, get_item_variants
from cataloging_service.api.combo_apis import create_combo, get_all_combos, get_combo, edit_combo
from cataloging_service.api.room_rack_rate_apis import (get_room_rack_rates, update_room_rack_rate)
from cataloging_service.api.restaurant_area_apis import create_area, get_areas, get_area, update_area, get_table,\
    get_tables, create_tables

from cataloging_service.api.kitchen_apis import create_kitchen, get_kitchen, get_kitchens, delete_kitchen,\
    update_kitchen

from cataloging_service.api.user_defined_enums import get_enums
from cataloging_service.api.tenant_config import get_tenant_configs, add_or_update_tenant_config
from cataloging_service.api.user_defined_enums import get_enums
from cataloging_service.api.v3 import property_search_api_method_view, property_koopan_api_method_view
from cataloging_service.api.v3.property_koopan import KoopanPropertySchema
from cataloging_service.api.v3.schemas.property import PropertySchema
from cataloging_service.api.validators import TenantConfigRequestValidator, MenuCreateRequestValidator, \
    MenuEditRequestValidator, ComboCreateRequestValidator, ComboEditRequestValidator, \
    ItemCreateRequestValidator, VariantGroupCreateRequestValidator, VariantGroupEditRequestValidator, \
    RestaurantAreaCreateRequestValidator, RestaurantAreaUpdateRequestValidator, \
    RestaurantTableBulkCreateRequestValidator, CurrencyConversionRateRequestValidator, KitchenCreateRequestValidator,\
    KitchenEditRequestValidator, ItemEditRequestValidator, MenuComboEditRequestValidator, \
    MenuItemEditRequestValidator
from core.common.api.api_response_schema import APIErrorSchema, PaginationSchema


views = [
    property_search_api_method_view,
    property_koopan_api_method_view,
    get_all_sku_categories_by_ids,
    get_all_sku_categories_by_codes,
    get_all_sku_by_codes,
    get_all_sku_by_room_configs,
    get_all_room_configs_by_sku,
    get_sku_category,
    get_sku_category_by_code,
    get_property_sku_categories,
    get_property_sku,
    get_property_skus,
    get_properties_skus,
    create_sku_under_a_property,
    update_sku_under_a_property,
    update_room_rack_rate,
    get_room_rack_rates,
    get_seller_type,
    get_all_legal_entities,
    get_legal_entity_by_state_id,
    add_room_type_configuration,
    update_room_type_configuration,
    get_room_type_configurations,
    add_property_room,
    update_property_room,
    get_property_rooms,
    get_room_types,
    get_provider,
    get_all_providers,
    get_provider_properties,
    get_all_properties,
    create_property,
    create_property_v1,
    get_property,
    get_property_v1,
    get_properties_v2,
    modify_property,
    modify_property_v1,
    get_property_images,
    get_property_images_v1,
    activate_sku_and_bundle,
    create_property_sku,
    get_property_by_pms_and_property_id,
    get_property_by_pms_and_property_id_v1,
    get_countries,
    get_states,
    get_cities,
    get_clusters,
    get_localities,
    get_micro_markets,
    get_regions,
    get_all_channels,
    get_channel,
    get_sub_channels_for_channel,
    get_all_applications,
    get_applications_for_channel,
    get_all_pricing_policies,
    get_property_amenities,
    get_property_amenity_summary,
    get_property_restaurants,
    get_property_bars,
    get_property_banquet_halls,
    get_room_amenities,
    get_sellers,
    get_seller_skus,
    get_menu_categories,
    get_restaurant_tables,
    get_tenant_configs,
    get_enums,
    add_or_update_tenant_config,
    create_menu,
    edit_menu,
    edit_menu_combo,
    get_all_menus,
    get_menu,
    get_all_combos,
    get_combo,
    create_combo,
    edit_combo,
    create_item,
    get_item,
    get_items,
    edit_item,
    search_variant_groups,
    create_variant_group,
    edit_variant_group,
    edit_menu_item,
    get_item_customisations,
    get_item_variants,
    create_area,
    get_area,
    get_areas,
    update_area,
    get_table,
    get_tables,
    create_tables,
    create_kitchen,
    update_kitchen,
    get_kitchens,
    get_kitchen,
    delete_kitchen,
    get_sku_categories_for_seller,
    get_currency_conversion_rates,
    create_currency_conversion_rate,
    get_all_test_properties,
]

schemas = [
    APIErrorSchema,
    PaginationSchema,
    PropertySchema,
    KoopanPropertySchema,
    SkuCategorySchema,
    SkuSchema,
    SellerResponseSchema,
    SellerSkuResponseSchema,
    MenuCategoryResponseSchema,
    RestaurantTablesResponseSchema,
    TenantConfigResponseSchema,
    UserDefinedEnumResponseSchema,
    UserDefinedEnumValueResponseSchema,
    TenantConfigRequestValidator,
    MenuCreateRequestValidator,
    MenuEditRequestValidator,
    MenuSchema,
    MenuDetailSchema,
    ComboSchema,
    ComboDetailSchema,
    ComboCreateRequestValidator,
    ComboEditRequestValidator,
    ItemCreateRequestValidator,
    VariantGroupCreateRequestValidator,
    VariantGroupEditRequestValidator,
    ItemSchema,
    ItemListSchema,
    VariantGroupSchema,
    MenuItemSchema,
    ItemVariantShortSchema,
    ItemCustomisationSchema,
    NewSkuSchema,
    RoomRackRateSchema,
    UpdateRoomRackRateSchema,
    UpdateSkuUnderPropertySchema,
    RestaurantAreaCreateRequestValidator,
    RestaurantAreaSchema,
    RestaurantTableSchema,
    RestaurantAreaDetailSchema,
    RestaurantTableBulkCreateRequestValidator,
    RestaurantAreaUpdateRequestValidator,
    KitchenCreateRequestValidator,
    KitchenEditRequestValidator,
    CurrencyConversionRateRequestValidator,
    CurrencyConversionRateResponseSchema,
    SkuSchemaV2,
    MenuItemListSchema,
    MenuComboSchema,
    ItemEditRequestValidator,
    MenuComboEditRequestValidator,
    MenuItemEditRequestValidator,
    KitchenSchema,
]




def init_swagger_docs(app):
    """Initialize OpenAPI 3.0.2 documentation using Flasgger"""

    spec = APISpec(
        title='Cataloging Service API',
        version='1.0.0',
        openapi_version='3.0.2',
        plugins=[MarshmallowPlugin()],
        info=dict(
            description='API documentation for Cataloging Service',
            contact=dict(
                name='Treebo Engineering',
                email='<EMAIL>'
            )
        )
    )

    for schema_class in schemas:
        try:
            spec.components.schema(schema_class.__name__, schema=schema_class)
        except DuplicateComponentNameError:
            continue

    swagger_config = {
        'headers': [],
        'specs': [
            {
                'endpoint': 'apispec',
                'route': '/cataloging-service/swagger/',
                'rule_filter': lambda rule: True,
                'model_filter': lambda tag: True,
            }
        ],
        'static_url_path': '/flasgger_static',
        'swagger_ui': True,
        'specs_route': '/cataloging-service/apidocs/',
        'openapi': '3.0.2'
    }

    Swagger(app, config=swagger_config, template=spec.to_dict())
