import pytest
import os

from treebo_commons.multitenancy.sqlalchemy import db_engine
from treebo_commons.multitenancy.sqlalchemy.db_engine import Base
import cataloging_service
from cataloging_service.app import create_app

_app = create_app()

pytest_plugins = [
    "itests.fixtures.conftest_fixtures",
    "itests.fixtures.conftest_repositories"
]


def ensure_valid_test_env_setup(app):
    assert app.config['DEBUG']
    assert app.config['TESTING'], "App Config 'TESTING' must be True for running tests"
    assert os.environ.get('APP_ENV') == 'testing', "APP_ENV should be 'testing' for running tests"
    database_uris = db_engine.get_database_uris()
    assert all(db_creds.dbname == 'cataloging_service_test' for tenant_id, db_creds in
               database_uris.items()), "Database name should be 'cataloging_service_test' for running tests"
    assert all(db_creds.host == 'localhost' for tenant_id, db_creds in
               database_uris.items()), "Database host should be 'localhost' for running tests"



ensure_valid_test_env_setup(_app)


@pytest.fixture(scope='session', autouse=True)
def app():
    ensure_valid_test_env_setup(_app)
    ctx = _app.test_request_context()
    ctx.push()

    print("===========> Dropping and re-creating tables")
    Base.metadata.drop_all(bind=db_engine.get_engine(None))
    Base.metadata.create_all(bind=db_engine.get_engine(None))
    print("=====>>>> Created tables")

    print("===========> Using app fixture")

    yield _app

    print("===========> Teardown app fixture")

    ctx.pop()


@pytest.fixture(scope='session')
def client(app):
    print("===========> Using client fixture")

    test_client = app.test_client()
    yield test_client
    print("===========> Teardown client fixture")

cataloging_service_test