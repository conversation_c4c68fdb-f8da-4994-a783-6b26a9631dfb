# This file is here because many Platforms as a Service look for
#	requirements.txt in the root directory of a project.
## These are auto-installed dependencies. Not directly needed by Catalog
# Flask-Mail-0.9.1 Flask-Principal-0.4.0 Mako-1.1.0 asn1crypto-1.2.0 blinker-1.4 cffi-1.13.2 idna-2.8
# passlib-1.7.2 pycparser-2.19 # python-editor-1.0.4 vine-1.3.0 webargs-5.5.2
alembic==0.9.1
amqp<=6.0.0
apispec==6.8.0
apispec-webframeworks==0.4.0
# Required by Flask Security & Passlib
bcrypt==3.1.7
click==6.7
coverage==4.3.4
cryptography==2.4.2
Flask==1.1.1
Flask-Admin==1.6.1
flask-apispec==0.11.4
# Removing Flask-Cache and using Flask-Caching instead, which is further extension of Flask-Cache
# Flask-Cache used old way to using flask extensions using `flask.ext.cache`.. which is no more supported
# See: https://github.com/thadeusb/flask-cache/issues/188
Flask-Caching==1.8.0
Flask-Login==0.3.2
flask-marshmallow==0.15.0
Flask-Script==2.0.5
Flask-Security==3.0.0
Flask-WTF==0.14.2
gevent==25.5.1
google-api-python-client==1.6.2
greenlet==3.2.2
gunicorn==19.8.0
httplib2==0.20.0
itsdangerous>=0.24,<2.1
Jinja2>=2.10.1
kombu==5.3.4
logstash-formatter==0.5.16
MarkupSafe==1.1.1
marshmallow==3.8.0
marshmallow-sqlalchemy==0.12.1
newrelic==8.11.0
#psycopg2==2.8.3
PyYAML==5.2
python-slugify==1.2.5
# Required by fuzzywuzzy
python-Levenshtein==0.27.1
pytz>=2020.1
redis==3.3.11
requests>=2.31.0
simplejson==3.17.0
six>=1.16.0
SQLAlchemy==1.4.3
Werkzeug==0.16
WTForms==2.1
fuzzywuzzy==0.17.0
whitenoise==5.0.1
pandas==2.2.0
python-dateutil>=2.7.2
boto3>=1.34.0
botocore>=1.34.0
urllib3>=1.26
sentry-sdk[flask]==0.10.2
factory-boy==2.10.0
aenum==2.2.6
freezegun==1.1.0
lxml==5.4.0
webargs==6.0.0